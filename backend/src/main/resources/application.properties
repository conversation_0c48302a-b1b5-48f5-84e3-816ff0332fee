spring.application.name=backend
server.port=8080

# Hibernate SQL + l?i validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql=TRACE
logging.level.org.springframework.orm.jpa=DEBUG
logging.level.org.hibernate.engine.internal=DEBUG


# Connect to MSSQL
spring.datasource.url=jdbc:sqlserver://${MSSQL_HOST}:${MSSQL_PORT};databaseName=${MSSQL_DATABASE};encrypt=true;trustServerCertificate=true;characterEncoding=UTF-8
spring.datasource.username=${MSSQL_USERNAME}
spring.datasource.password=${MSSQL_PASSWORD}
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver

# Hibernate config
spring.jpa.hibernate.ddl-auto=none
spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl

# Flyway config
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
spring.flyway.database=mssql

#API
api.prefix = api/v1

#JWT
jwt.secret=CHUNGTOILABONNGHIENVIDEOLACDUYTCUAMAYEMGAITRENFACEBOOKVATRENTIKTOK
jwt.expirationMs=86400000

payment.vnpay.url=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
payment.vnpay.returnUrl=http://localhost:8080/api/v1/payment/vnpay-return
payment.vnpay.tmnCode=CGRJ2V96
payment.vnpay.secretKey=P055XEPU5XMKEB8UUA8K3Q5K1Z3DZNUZ
payment.vnpay.version=2.1.0
payment.vnpay.command=pay
payment.vnpay.orderType=other


#T?t check validate migration
spring.flyway.validate-on-migrate=false

PAYOS_CLIENT_ID=a4ecfc53-e944-4e04-9701-531325501b60
PAYOS_API_KEY=86998ab2-943b-4734-8332-5c13be219d92
PAYOS_CHECKSUM_KEY=ead95ffd07e39550215017f61aa7da34c134c678dc0d5b3f925182e3b0b76a83



spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=bodrcsljjwlrdpqn
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true


frontend.order.detail.url=http://localhost:5173/order-detail/