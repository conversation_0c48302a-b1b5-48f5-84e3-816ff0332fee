package com.hyperformancelabs.backend.controller;

import com.hyperformancelabs.backend.dto.*;
import com.hyperformancelabs.backend.exception.ResourceNotFoundException;
import com.hyperformancelabs.backend.payload.ApiResponse;
import com.hyperformancelabs.backend.payload.ApiResponseStatus;
import com.hyperformancelabs.backend.payload.PagedResponse;
import com.hyperformancelabs.backend.service.ProductDetailService;
import com.hyperformancelabs.backend.service.impl.ProductServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/products")
public class ProductController {

    @Autowired
    private ProductServiceImpl productService;
    
    @Autowired
    private ProductDetailService productDetailService;

    private static final Logger logger = LoggerFactory.getLogger(ProductController.class);

    @GetMapping
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllProducts(@RequestParam(defaultValue = "0") int page) {
        try {
            Page<ProductDTO> products = productService.getAllProducts(page);

            Map<String, Object> data = new HashMap<>();
            data.put("items", products.getContent()); // list sản phẩm
            data.put("currentPage", products.getNumber());
            data.put("totalItems", products.getTotalElements());
            data.put("totalPages", products.getTotalPages());

            return ResponseEntity.ok(
                new ApiResponse<>(
                    ApiResponseStatus.SUCCESS_CODE,
                    ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
                    data
                )
            );
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(
                new ApiResponse<>(
                    ApiResponseStatus.BAD_REQUEST_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    e.getMessage(),
                    null
                )
            );
        }
    }

//    @GetMapping("/brand/{brandName}")
//    public ResponseEntity<ApiResponse<Page<ProductDTO>>> getProductsByBrand(
//            @PathVariable String brandName,
//            @RequestParam(defaultValue = "0") int page) {
//        try {
//            Page<ProductDTO> products = productService.getProductsByBrand(brandName, page);
//            return ResponseEntity.ok(
//                new ApiResponse<>(
//                    ApiResponseStatus.SUCCESS_CODE,
//                    ApiResponseStatus.SUCCESS_STATUS,
//                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
//                    products
//                )
//            );
//        } catch (Exception e) {
//            return ResponseEntity.badRequest().body(
//                new ApiResponse<>(
//                    ApiResponseStatus.BAD_REQUEST_CODE,
//                    ApiResponseStatus.ERROR_STATUS,
//                    e.getMessage(),
//                    null
//                )
//            );
//        }
//    }
@GetMapping("/by-brand")
public ResponseEntity<ApiResponse<Map<String, Object>>> getProductsByBrandPaged(
        @RequestParam String brandName,
        @RequestParam(defaultValue = "0") int page
) {
    try {
        PagedResponse<ProductCard> pagedResponse = productService.getProductVariantsByBrandNamePaged(brandName, page);
        
        Map<String, Object> data = new HashMap<>();
        data.put("items", pagedResponse.getContent());
        data.put("currentPage", pagedResponse.getPageNumber());
        data.put("totalItems", pagedResponse.getTotalItems());
        data.put("totalPages", pagedResponse.getTotalPages());
        
        return ResponseEntity.ok(
            new ApiResponse<>(
                ApiResponseStatus.SUCCESS_CODE,
                ApiResponseStatus.SUCCESS_STATUS,
                ApiResponseStatus.GET_SUCCESS_MESSAGE,
                data
            )
        );
    } catch (Exception e) {
        return ResponseEntity.badRequest().body(
            new ApiResponse<>(
                ApiResponseStatus.BAD_REQUEST_CODE,
                ApiResponseStatus.ERROR_STATUS,
                e.getMessage(),
                null
            )
        );
    }
}


    @GetMapping("/top-selling-products")
    public ResponseEntity<ApiResponse<List<TopSellingProductDTO>>> getProducts(
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestParam(value = "limit", defaultValue = "10") int limit
    ) {
        try {
            if ("bestseller".equalsIgnoreCase(sort)) {
                List<TopSellingProductDTO> topSellingProducts = productService.getTopSellingProducts(category, limit)
                        .stream()
                        .limit(limit)
                        .toList();
                
                return ResponseEntity.ok(
                    new ApiResponse<>(
                        ApiResponseStatus.SUCCESS_CODE,
                        ApiResponseStatus.SUCCESS_STATUS,
                        ApiResponseStatus.GET_SUCCESS_MESSAGE,
                        topSellingProducts
                    )
                );
            }
            return ResponseEntity.ok(
                new ApiResponse<>(
                    ApiResponseStatus.SUCCESS_CODE,
                    ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
                    List.of()
                )
            );
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(
                new ApiResponse<>(
                    ApiResponseStatus.BAD_REQUEST_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    e.getMessage(),
                    null
                )
            );
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<PagedResponse<ProductCard>>> searchProducts(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "25") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<ProductCard> productPage = productService.searchProductsByName(keyword, pageable);

        PagedResponse<ProductCard> response = new PagedResponse<>(
                productPage.getContent(),
                page,
                size,
                productPage.getTotalElements()
        );

        return ResponseEntity.ok(new ApiResponse<>(
                ApiResponseStatus.SUCCESS_CODE,
                ApiResponseStatus.SUCCESS_STATUS,
                ApiResponseStatus.GET_SUCCESS_MESSAGE,
                response
        ));
    }


    @GetMapping("/random-10")
    public List<Random10Product> getRandom10Products() {
        return productService.getRandom10Product();
    }

    @GetMapping("/variants")
    public List<ProductCard> getProductVariantsGroupedByProduct() {
        return productService.getProductVariantsGroupedByProduct();
    }


    @GetMapping("/flash-sale")
    public ResponseEntity<ApiResponse<List<ProductCard>>> getFlashSaleProducts() {
        try {
            List<ProductCard> products = productService.getFlashSaleProducts();
            return ResponseEntity.ok(
                new ApiResponse<>(
                    ApiResponseStatus.SUCCESS_CODE,
                    ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
                    products
                )
            );
        } catch (Exception e) {
            logger.error("Error retrieving flash sale products", e);
            return ResponseEntity.status(ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE).body(
                new ApiResponse<>(
                    ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    e.getMessage(),
                    null
                )
            );
        }
    }

    @PostMapping("/filter")
    public ResponseEntity<ApiResponse<Map<String, Object>>> filterProducts(
            @RequestBody FilterRequestDTO request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "price") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir
    ) {
        // Validate price range
        if (request.getMinPrice() != null && request.getMaxPrice() != null
                && request.getMinPrice().compareTo(request.getMaxPrice()) > 0) {
            return ResponseEntity.badRequest().body(
                new ApiResponse<>(ApiResponseStatus.BAD_REQUEST_CODE, ApiResponseStatus.ERROR_STATUS,
                        "minPrice cannot be greater than maxPrice", null)
            );
        }
        // Validate pagination
        if (page < 0 || size <= 0) {
            return ResponseEntity.badRequest().body(
                new ApiResponse<>(ApiResponseStatus.BAD_REQUEST_CODE, ApiResponseStatus.ERROR_STATUS,
                        "Invalid pagination parameters", null)
            );
        }
        List<ProductCard> cards = productService.filterProducts(request);
        // Sorting
        if ("price".equalsIgnoreCase(sortBy)) {
            cards.sort((a, b) -> {
                BigDecimal aMin = a.getVolumePrices().stream()
                        .map(VolumePriceDTO::getPrice)
                        .reduce(BigDecimal::min)
                        .orElse(BigDecimal.ZERO);
                BigDecimal bMin = b.getVolumePrices().stream()
                        .map(VolumePriceDTO::getPrice)
                        .reduce(BigDecimal::min)
                        .orElse(BigDecimal.ZERO);
                return sortDir.equalsIgnoreCase("asc")
                        ? aMin.compareTo(bMin)
                        : bMin.compareTo(aMin);
            });
        } else if ("name".equalsIgnoreCase(sortBy)) {
            cards.sort((a, b) -> sortDir.equalsIgnoreCase("asc")
                    ? a.getProductName().compareToIgnoreCase(b.getProductName())
                    : b.getProductName().compareToIgnoreCase(a.getProductName()));
        }
        // Pagination
        int totalItems = cards.size();
        int from = page * size;
        int toIndex = Math.min(from + size, totalItems);
        List<ProductCard> pageItems = (from >= totalItems)
                ? Collections.emptyList()
                : cards.subList(from, toIndex);
        Map<String, Object> data = new HashMap<>();
        data.put("items", pageItems);
        data.put("currentPage", page);
        data.put("totalItems", totalItems);
        data.put("totalPages", (int) Math.ceil((double) totalItems / size));
        return ResponseEntity.ok(
            new ApiResponse<>(ApiResponseStatus.SUCCESS_CODE, ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE, data)
        );
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ProductDetailDTO>> getProductDetail(@PathVariable Integer id) {
        try {
            ProductDetailDTO productDetail = productService.getProductDetailById(id);
            return ResponseEntity.ok(
                new ApiResponse<>(
                    ApiResponseStatus.SUCCESS_CODE,
                    ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
                    productDetail
                )
            );
        } catch (ResourceNotFoundException e) {
            logger.warn("Product not found: {}", e.getMessage());
            return ResponseEntity.status(ApiResponseStatus.NOT_FOUND_CODE).body(
                new ApiResponse<>(
                    ApiResponseStatus.NOT_FOUND_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    e.getMessage(),
                    null
                )
            );
        } catch (Exception e) {
            logger.error("Error processing product detail request for ID: " + id, e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : "Error processing request";
            return ResponseEntity.status(ApiResponseStatus.BAD_REQUEST_CODE).body(
                new ApiResponse<>(
                    ApiResponseStatus.BAD_REQUEST_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    errorMessage,
                    null
                )
            );
        }
    }

    @GetMapping("/category")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getProductsByGender(
            @RequestParam("gender") String gender,
            @RequestParam(value = "page", defaultValue = "0") int page
    ) {
        try {
            // Normalize input
            String normalizedGender = capitalizeFirstLetter(gender.trim().toLowerCase());

            if (!List.of("Women", "Men", "Unisex").contains(normalizedGender)) {
                return ResponseEntity.badRequest().body(
                        new ApiResponse<>(
                                ApiResponseStatus.BAD_REQUEST_CODE,
                                ApiResponseStatus.ERROR_STATUS,
                                "Giá trị gender không hợp lệ. Chỉ chấp nhận: Women, Men, Unisex.",
                                null
                        )
                );
            }

            // Gọi service
            PagedResponse<ProductCard> pagedResponse = productService.getProductVariantsByGenderPaged(normalizedGender, page);

            // Tạo data map
            Map<String, Object> data = new HashMap<>();
            data.put("items", pagedResponse.getContent());
            data.put("currentPage", pagedResponse.getPageNumber());
            data.put("totalItems", pagedResponse.getTotalItems());
            data.put("totalPages", pagedResponse.getTotalPages());

            // Trả response
            return ResponseEntity.ok(
                    new ApiResponse<>(
                            ApiResponseStatus.SUCCESS_CODE,
                            ApiResponseStatus.SUCCESS_STATUS,
                            ApiResponseStatus.GET_SUCCESS_MESSAGE,
                            data
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(
                    new ApiResponse<>(
                            ApiResponseStatus.BAD_REQUEST_CODE,
                            ApiResponseStatus.ERROR_STATUS,
                            e.getMessage(),
                            null
                    )
            );
        }
    }

    private String capitalizeFirstLetter(String input) {
        if (input == null || input.isEmpty()) return input;
        return input.substring(0, 1).toUpperCase() + input.substring(1);
    }
    
    @GetMapping("/filter-options")
    public ResponseEntity<ApiResponse<FilterOptionDTO>> getFilterOptions() {
        try {
            FilterOptionDTO filterOptions = productDetailService.getFilterOptions();
            return ResponseEntity.ok(
                new ApiResponse<>(
                    ApiResponseStatus.SUCCESS_CODE,
                    ApiResponseStatus.SUCCESS_STATUS,
                    ApiResponseStatus.GET_SUCCESS_MESSAGE,
                    filterOptions
                )
            );
        } catch (Exception e) {
            logger.error("Error retrieving filter options", e);
            return ResponseEntity.badRequest().body(
                new ApiResponse<>(
                    ApiResponseStatus.BAD_REQUEST_CODE,
                    ApiResponseStatus.ERROR_STATUS,
                    e.getMessage(),
                    null
                )
            );
        }
    }

    @GetMapping("/new-product")
    public ResponseEntity<ApiResponse<PagedResponse<ProductCard>>> getNewProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "25") int size) {
        try {
            PagedResponse<ProductCard> products = productService.getNewProducts(page, size);
            return ResponseEntity.ok(
                    new ApiResponse<>(
                            ApiResponseStatus.SUCCESS_CODE,
                            ApiResponseStatus.SUCCESS_STATUS,
                            ApiResponseStatus.GET_SUCCESS_MESSAGE,
                            products
                    )
            );
        } catch (Exception e) {
            logger.error("Error retrieving new products", e);
            return ResponseEntity.status(ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE).body(
                    new ApiResponse<>(
                            ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE,
                            ApiResponseStatus.ERROR_STATUS,
                            e.getMessage(),
                            null
                    )
            );
        }
    }


    @GetMapping("/best-seller")
    public ResponseEntity<ApiResponse<PagedResponse<ProductCard>>> getBestSellerProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "25") int size) {
        try {
            PagedResponse<ProductCard> products = productService.getBestSellerProducts(page, size);
            return ResponseEntity.ok(
                    new ApiResponse<>(
                            ApiResponseStatus.SUCCESS_CODE,
                            ApiResponseStatus.SUCCESS_STATUS,
                            ApiResponseStatus.GET_SUCCESS_MESSAGE,
                            products
                    )
            );
        } catch (Exception e) {
            logger.error("Error retrieving new products", e);
            return ResponseEntity.status(ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE).body(
                    new ApiResponse<>(
                            ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE,
                            ApiResponseStatus.ERROR_STATUS,
                            e.getMessage(),
                            null
                    )
            );
        }
    }

    @GetMapping("/season")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSeasonProducts(
            @RequestParam String season,
            @RequestParam(defaultValue = "0") int page
    ) {
        try {
            PagedResponse<ProductCard> pagedResponse = productService.getSeasonProducts(season, page);

            Map<String, Object> data = new HashMap<>();
            data.put("items", pagedResponse.getContent());
            data.put("currentPage", pagedResponse.getPageNumber());
            data.put("totalItems", pagedResponse.getTotalItems());
            data.put("totalPages", pagedResponse.getTotalPages());

            return ResponseEntity.ok(
                    new ApiResponse<>(
                            ApiResponseStatus.SUCCESS_CODE,
                            ApiResponseStatus.SUCCESS_STATUS,
                            ApiResponseStatus.GET_SUCCESS_MESSAGE,
                            data
                    )
            );
        } catch (Exception e) {
            return ResponseEntity.status(ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE).body(
                    new ApiResponse<>(
                            ApiResponseStatus.INTERNAL_SERVER_ERROR_CODE,
                            ApiResponseStatus.ERROR_STATUS,
                            e.getMessage(),
                            null
                    )
            );
        }
    }


}
