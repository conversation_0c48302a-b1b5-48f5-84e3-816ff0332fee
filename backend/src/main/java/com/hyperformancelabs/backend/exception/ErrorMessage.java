package com.hyperformancelabs.backend.exception;

public class ErrorMessage {
    public static final String CUSTOMER_NOT_FOUND = "Customer not found";
    public static final String NO_ACTIVE_CART = "No active cart found";
    public static final String NO_SELECTED_ITEMS = "No selected items in the cart to place an order";
    public static final String PRODUCT_NOT_FOUND = "Không tìm thấy sản phẩm: ";
    public static final String ORDER_NOT_FOUND = "Không tìm thấy đơn hàng với ID = ";
    public static final String CUSTOMER_INFO_MISSING = "Đơn hàng không có thông tin khách hàng.";
    public static final String EMAIL_PHONE_IN_USE = "Email hoặc số điện thoại đã được sử dụng. Vui lòng đăng nhập.";
    public static final String EMPTY_ORDER = "Không có sản phẩm nào trong đơn hàng";
    public static final String ORDER_DETAIL_ERROR = "Lỗi khi lấy chi tiết đơn hàng: ";
    public static final String ORDER_ITEM_NOT_FOUND = "Không tìm thấy sản phẩm trong đơn hàng";
    public static final String ORDER_ITEM_QUANTITY_EXCEEDED = "Số lượng sản phẩm trong đơn hàng vượt quá số lượng trong kho";


    public static final String PAYMENT_NOT_FOUND = "Không tìm thấy thanh toán";
    public static final String PAYMENT_METHOD_NOT_FOUND = "Không tìm thấy phương thức thanh toán";

    public static final String PRODUCT_DETAIL_NOT_FOUND = "Không tìm thấy chi tiết sản phẩm";
    public static final String PRODUCTVARIANT_ID_NOT_FOUND = "Không tìm thấy sản phẩm";


    public static final String USER_NOT_FOUND = "Không tìm thấy người dùng";
    public static final String CART_NOT_FOUND = "Không tìm thấy giỏ hàng";
    public static final String PRODUCT_VARIANT_NOT_FOUND = "Không tìm thấy biến thể sản phẩm";
    public static final String CART_ITEM_NOT_FOUND = "Không tìm thấy sản phẩm trong giỏ hàng";
    public static final String ITEM_NOT_FOUND = "Không tìm thấy mục cần xóa";



}
