package com.hyperformancelabs.backend.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentInfo {
    private String method;
    private String status;
    private BigDecimal amount;
    private LocalDateTime paymentDate;
    private String transactionId;
    private String currency;
}
