package com.hyperformancelabs.backend.service.impl;

import com.hyperformancelabs.backend.model.Order;
import com.hyperformancelabs.backend.model.OrderItem;
import com.hyperformancelabs.backend.service.EmailService;
import com.hyperformancelabs.backend.dto.PaymentInfo;
import com.hyperformancelabs.backend.dto.ShipmentInfo;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${application.name:Perfume Store}")
    private String storeName;

    @Value("${frontend.order.detail.url}")
    private String orderDetailUrl;

    @Override
    public void sendOrderConfirmation(String toEmail, Order order, ShipmentInfo shipment, PaymentInfo payment) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(storeName + " - Xác nhận đơn hàng #" + order.getOrderId());

            String emailContent = buildOrderConfirmationEmail(order, shipment, payment);
            helper.setText(emailContent, true);

            mailSender.send(message);
        } catch (MessagingException e) {
            System.err.println("Gửi email thất bại: " + e.getMessage());
        }
    }

    private String buildOrderConfirmationEmail(Order order, ShipmentInfo shipment, PaymentInfo payment) {
        DecimalFormat formatter = new DecimalFormat("#,###");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");

        BigDecimal shippingFee = order.getShippingFee() != null ? order.getShippingFee() : BigDecimal.ZERO;
        BigDecimal productTotal = order.getTotalAmount().subtract(shippingFee);

        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html><html><head><meta charset=\"UTF-8\">")
                .append("<style>")
                .append("body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }")
                .append(".container { max-width: 600px; margin: 0 auto; border: 1px solid #ddd; border-radius: 5px; padding: 20px; }")
                .append(".header { text-align: center; padding-bottom: 20px; border-bottom: 1px solid #ddd; }")
                .append(".section { margin: 20px 0; }")
                .append(".product-item { display: flex; border-bottom: 1px solid #eee; padding: 10px 0; }")
                .append(".product-details { flex-grow: 1; }")
                .append(".price-details { width: 120px; text-align: right; }")
                .append(".total-section { font-weight: bold; margin-top: 20px; text-align: right; }")
                .append(".footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }")
                .append(".btn { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 15px; }")
                .append("</style></head><body>")
                .append("<div class=\"container\">");

        // Header with logo
        html.append("<div class=\"header\">")
                .append("<img src=\"src/main/resources/static/images/stamp.png\" alt=\"Logo\" style=\"max-height: 60px;\">")
                .append("<h2>").append(storeName).append("</h2>")
                .append("<h3>Xác nhận đơn hàng #").append(order.getOrderId()).append("</h3>")
                .append("</div>");

        html.append("<div class=\"section\">")
                .append("<p>Kính gửi ").append(order.getCustomer().getName()).append(",</p>")
                .append("<p>Cảm ơn bạn đã đặt hàng tại ").append(storeName).append(". Đơn hàng của bạn đã được ghi nhận.</p>")
                .append("</div>");

        html.append("<div class=\"section\">")
                .append("<h3>Chi tiết đơn hàng</h3>")
                .append("<p><strong>Mã đơn hàng:</strong> ").append(order.getOrderId()).append("</p>")
                .append("<p><strong>Ngày đặt hàng:</strong> ").append(order.getOrderDate().format(dateTimeFormatter)).append("</p>")
                .append("<p><strong>Trạng thái:</strong> ").append(getOrderStatusInVietnamese(order.getOrderStatus())).append("</p>");

        if (order.getEstimatedDeliveryDate() != null) {
            html.append("<p><strong>Ngày giao dự kiến:</strong> ").append(order.getEstimatedDeliveryDate().format(dateFormatter)).append("</p>");
        }
        html.append("</div>");

        // Product list
        html.append("<div class=\"section\"><h3>Sản phẩm</h3>");
        for (OrderItem item : order.getOrderItems()) {
            html.append("<div class=\"product-item\">")
                    .append("<div class=\"product-details\">")
                    .append("<p><strong>").append(item.getProductVariant().getProduct().getProductName()).append("</strong><br>")
                    .append("Dung tích: ").append(item.getProductVariant().getVolume()).append("ml<br>")
                    .append("Số lượng: ").append(item.getQuantity());
            if (item.getNote() != null && !item.getNote().isEmpty()) {
                html.append("<br>Ghi chú: ").append(item.getNote());
            }
            html.append("</p></div>")
                    .append("<div class=\"price-details\">")
                    .append("<p>").append(formatter.format(item.getUnitPrice())).append(" VND</p>")
                    .append("</div></div>");
        }

        html.append("<div class=\"total-section\">")
                .append("<p>Tổng tiền hàng: ").append(formatter.format(productTotal)).append(" VND</p>")
                .append("<p>Phí vận chuyển: ").append(formatter.format(shippingFee)).append(" VND</p>")
                .append("<p>Tổng thanh toán: ").append(formatter.format(order.getTotalAmount())).append(" VND</p>")
                .append("</div></div>");

        // Shipping info
        html.append("<div class=\"section\"><h3>Thông tin giao hàng</h3>")
                .append("<p><strong>Người nhận:</strong> ").append(order.getCustomer().getName()).append("</p>")
                .append("<p><strong>Điện thoại:</strong> ").append(order.getCustomer().getPhoneNumber()).append("</p>")
                .append("<p><strong>Địa chỉ:</strong> ").append(order.getShippingAddress()).append("</p>")
                .append("<p><strong>Đơn vị vận chuyển:</strong> ").append(order.getShippingOption()).append("</p>");

        if (shipment != null && shipment.getTrackingNumber() != null) {
            html.append("<p><strong>Mã vận đơn:</strong> ").append(shipment.getTrackingNumber()).append("</p>");
        }

        html.append("</div>");

        // Payment info
        if (payment != null) {
            html.append("<div class=\"section\"><h3>Thông tin thanh toán</h3>")
                    .append("<p><strong>Phương thức:</strong> ").append(payment.getMethod()).append("</p>")
                    .append("<p><strong>Trạng thái:</strong> ").append(getPaymentStatusInVietnamese(payment.getStatus())).append("</p>");

            if (payment.getTransactionId() != null) {
                html.append("<p><strong>Mã giao dịch:</strong> ").append(payment.getTransactionId()).append("</p>");
            }
            if (payment.getPaymentDate() != null) {
                html.append("<p><strong>Ngày thanh toán:</strong> ")
                        .append(payment.getPaymentDate().format(dateTimeFormatter)).append("</p>");
            }
            html.append("</div>");
        }

        // Button
        html.append("<div class=\"section\" style=\"text-align:center;\">")
                .append("<a class=\"btn\" href=\"").append(orderDetailUrl).append(order.getOrderId()).append("\">Xem đơn hàng</a>")
                .append("</div>");

        // Footer
        html.append("<div class=\"footer\">")
                .append("<p>Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi qua email hoặc hotline.</p>")
                .append("<p>Đây là email tự động, vui lòng không trả lời.</p>")
                .append("<p>© 2025 ").append(storeName).append(". All rights reserved.</p>")
                .append("</div></div></body></html>");

        return html.toString();
    }

    private String getOrderStatusInVietnamese(String status) {
        return switch (status.toLowerCase()) {
            case "processing" -> "Đang xử lý";
            case "shipping" -> "Đang vận chuyển";
            case "delivered" -> "Đã giao hàng";
            case "cancelled" -> "Đã hủy";
            case "waitpayment" -> "Chờ thanh toán";
            case "paid" -> "Đã thanh toán";
            default -> status;
        };
    }

    private String getPaymentStatusInVietnamese(String status) {
        return switch (status.toLowerCase()) {
            case "pending" -> "Chờ thanh toán";
            case "completed" -> "Đã thanh toán";
            case "failed" -> "Thất bại";
            case "refunded" -> "Đã hoàn tiền";
            default -> status;
        };
    }
}
