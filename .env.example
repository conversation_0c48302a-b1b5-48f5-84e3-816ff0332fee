# ===========================
# SQL Server Configuration
# ===========================

MSSQL_HOST=localhost # Hostname or IP address of the SQL Server
MSSQL_DATABASE=EnterpriseLFSDB # Default database name - Should not be changed
MSSQL_SA_PASSWORD=Luxury@Fragrance # Strong password for SA user
MSSQL_USERNAME=admin # Username for SQL Server
MSSQL_PASSWORD=Luxury@Fragrance # Password for SQL Server

# ===========================
# Docker Configuration
# ===========================

CONTAINER_NAME=lfs_mssql # Name of the Docker container
MSSQL_PORT=1433 # Port for SQL Server
PLATFORM=linux/amd64 # Platform for the Docker image
MSSQL_PID=Express # SQL Server edition (Express, Developer, etc.)

# ===========================
# Backend Configuration
# ===========================

SERVER_PORT=8080 # Port for the backend server