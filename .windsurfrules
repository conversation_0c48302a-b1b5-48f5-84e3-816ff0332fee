# Full-Stack E-Commerce Website "Shop Nước Hoa Xa Xỉ" - Project Documentation

## Project Context

### Project Overview

This project involves the development of a full-stack e-commerce website called "Shop Nước Hoa Xa Xỉ" (Luxury Perfume Shop). It is a platform for selling luxury perfumes, consisting of a backend, frontend, and database. The tech stack includes:

* **Frontend**: React.js (Admin & User UI) with Vite for development
* **Backend**: Spring Boot for RESTful API services
* **Database**: SQL Server for data storage

### Local Development Setup

All services are running locally on the default ports:

* **Backend**: `http://localhost:8080`
* **Frontend (Admin & User UI)**: `http://localhost:5173`
* **Database**: `localhost:1433`

> Note: You **do not need to manually start services** as they are always assumed to be running.

## Project Structure

```plaintext
shop-nuoc-hoa/
├── admin/                    # Admin UI (React.js + Vite)
│   ├── public/               # Static files
│   ├── src/                  # Source code
│   │   ├── assets/           # Assets
│   │   ├── Components/       # Components
│   │   ├── context/          # Context
│   │   ├── pages/            # Pages
│   │   ├── services/         # Services
│   ├── index.html            # HTML template
│   ├── eslint.config.js      # ESLint configuration
│   ├── package-lock.json     # Package lock file
│   ├── package.json          # Package configuration
│   ├── postcss.config.js     # PostCSS configuration
│   ├── README.md             # Project documentation (currently empty)
│   ├── tailwind.config.js    # Tailwind CSS configuration
│   ├── vite.config.js        # Vite configuration
├── frontend/                 # User UI (React.js + Vite)
│   ├── public/               
│   ├── src/
│   │   ├── assets/           # Assets
│   │   ├── Components/       # Components
│   │   ├── context/          # Context
│   │   ├── pages/            # Pages
│   │   ├── services/         # Services
│   ├── index.html            # HTML template
│   ├── eslint.config.js      # ESLint configuration
│   ├── package-lock.json     # Package lock file
│   ├── package.json          # Package configuration
│   ├── postcss.config.js     # PostCSS configuration
│   ├── README.md             # Project documentation (currently empty)
│   ├── tailwind.config.js    # Tailwind CSS configuration
│   ├── vite.config.js        # Vite configuration
├── backend/                  # Spring Boot backend
│   ├── src/main/resources/
│   │   ├── db/migration/     # Database schema
│   │   │   ├── V1__init_schema.sql
│   │   │   ├── V2__add_constraint.sql
│   │   ├── application.properties  # API and DB config
│   ├── src/main/java/com/hyperformancelabs/backend/
│   │   ├── config/
│   │   │   ├── ApiConfig.java    # API configuration
│   │   │   ├── SecurityConfig.java # Security configuration
│   │   │   ├── WebConfig.java    # Web configuration
│   │   ├── controller/           # API controllers
│   │   ├── dto/                  # Data transfer objects
│   │   ├── exception/            # Exception handling
│   │   ├── model/                # Data models
│   │   ├── payload/              # Payload classes
│   │   ├── repository/           # Data access
│   │   ├── service/              # Business logic
│   │   ├── util/                 # Utility classes
├── database/                   # Manage server database
├── .env                        # Environment variables (This is .env.local template with "...", do not fill)
├── .env.local                  # Local environment variables
├── .env.example                # Example environment variables (This is .env.local template with comments)
```

### All necessary database information is described below:

It's include all table name, column name, data type, primary key, foreign key, check constraint, unique constraint, default value, sample data.

```
Employee:
  employee_id: int, PK, identity
  username: varchar(50), unique, not null
  password: varchar(255), not null
  full_name: nvarchar(100), not null, check(LEN(LTRIM(RTRIM(full_name)))>0)
  phone_number: varchar(20), not null, check(phone_number NOT LIKE '%[^0-9 ()+-]%')
  email: varchar(100), unique, check(email LIKE '%_@__%.__%')
  address: nvarchar(255), not null, check(LEN(LTRIM(RTRIM(address)))>0)
  status: varchar(20), not null, default(active), check(status IN ('active','inactive','on_leave'))
  start_date: date, not null, default(GETDATE()), check(start_date<=CAST(GETDATE() AS DATE))
  last_login: datetime, check(last_login>=start_date)
  date_of_birth: date, check(date_of_birth<CAST(GETDATE() AS DATE))
  profile_picture_url: varchar(255)
  sample:
    - username: vhphat0807, password: admin@123, full_name: Vũ Hoàng Phát, phone_number: 708643765, email: <EMAIL>, address: 122 Hoàng Quốc Việt, Cầu Giấy, Hà Nội, status: active, start_date: 2025-03-10, date_of_birth: 2025-04-10, profile_picture_url: https://www.vietcap.com.vn/api/cms-api/uploads/contents/202211/lam-phat-la-gi.jpg

Role:
  role_id: int, PK, identity
  role_name: nvarchar(50), unique, not null, check(LEN(LTRIM(RTRIM(role_name)))>0)
  role_description: nvarchar(max)
  is_default: bit, not null, default(0)
  status: varchar(20), not null, default(active), check(status IN ('active','inactive'))
  constraints:
    unique_index: UQ_Role_IsDefault_True, is_default, where(is_default=1)
  sample:
    - role_name: System Admin, role_description: Administrator with full access, is_default: 0, status: active

EmployeeRole:
  employee_role_id: int, PK, identity
  employee_id: int, not null
  role_id: int, not null
  status: varchar(20), not null, default(active), check(status IN ('active','inactive'))
  constraints:
    FK:
      - employee_id: Employee(employee_id), CASCADE
      - role_id: Role(role_id), CASCADE
    unique: UQ_EmployeeRole, employee_id, role_id
  sample:
    - employee_id: 1, role_id: 1, status: active

Permission:
  permission_id: int, PK, identity
  permission_name: nvarchar(50), unique, not null, check(LEN(LTRIM(RTRIM(permission_name)))>0)
  permission_description: nvarchar(max)
  sample:
    - permission_name: employee.view, permission_description: View employee information

RolePermission:
  role_permission_id: int, PK, identity
  role_id: int, not null
  permission_id: int, not null
  constraints:
    FK:
      - role_id: Role(role_id), CASCADE
      - permission_id: Permission(permission_id), CASCADE
    unique: UQ_RolePermission, role_id, permission_id
  sample:
    - role_id: 1, permission_id: 1

Material:
  material_id: int, PK, identity
  material_name: varchar(100), unique, not null, check(LEN(LTRIM(RTRIM(material_name)))>0)
  description: nvarchar(max)
  unit: varchar(20), not null, check(LEN(LTRIM(RTRIM(unit)))>0)
  quantity_in_stock: int, not null, default(0), check(quantity_in_stock>=0)
  reorder_level: int, check(reorder_level>=0)
  price: decimal(10,2), not null, check(price>=0)
  sample:
    - material_name: 2ml Spray Bottle, description: Mini glass spray bottle, unit: pcs, quantity_in_stock: 100, reorder_level: 20, price: 3000

MaterialTransaction:
  material_transaction_id: int, PK, identity
  materialstagram_id: int, not null
  performed_by: int, not null
  transaction_date: datetime, not null, check(transaction_date<=GETDATE())
  before_quantity: int, check(before_quantity>=0)
  quantity: int, not null
  after_quantity: int, check(after_quantity>=0)
  transaction_type: varchar(20), not null, check(transaction_type IN ('import','export','adjust'))
  reason: nvarchar(max)
  note: nvarchar(max)
  cost_price: decimal(10,2), check(cost_price>=0)
  constraints:
    FK:
      - material_id: Material(material_id)
      - performed_by: Employee(employee_id)
  sample:
    - material_id: 1, performed_by: 1, transaction_date: 2025-04-01 08:30:00, before_quantity: 100, quantity: 50, after_quantity: 150, transaction_type: import, reason: Restocking inventory, note: Imported new batch of 2ml spray bottles, cost_price: 3000

Brand:
  brand_id: int, PK, identity
  brand_name: nvarchar(100), unique, not null, check(LEN(LTRIM(RTRIM(brand_name)))>0)
  brand_description: nvarchar(max)
  country_of_origin: nvarchar(100)
  logo_url: varchar(255)
  website_url: varchar(255), check(website_url LIKE 'http%')
  sample:
    - brand_name: Dolce & Gabbana, brand_description: Italian luxury fashion brand, country_of_origin: Italy, logo_url: https://img.fragrancex.com/images/l/29/us/0/homeimage_mobile.jpg, website_url: https://www.dolcegabbana.com

Product:
  product_id: int, PK, identity
  brand_id: int, not null
  product_name: nvarchar(100), not null, check(LEN(LTRIM(RTRIM(product_name)))>0)
  description: nvarchar(max)
  image_url: varchar(500)
  constraints:
    FK: brand_id: Brand(brand_id)
    unique: UQ_Product_Name_Brand, product_name, brand_id
  sample:
    - brand_id: 32, product_name: Guerlain Mon Florale, description: Elegant feminine fragrance, image_url: https://assets.goldenscent.com/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/3/3/3346470133990_guerlain_guerlain_mon_florale_edp_100_ml_1.jpg

ProductVariant:
  product_variant_id: int, PK, identity
  product_id: int, not null
  volume: int, not null, check(volume>0)
  price: decimal(10,2), not null, check(price>=0)
  discount_price: decimal(10,2), check(discount_price<=price)
  quantity_in_stock: int, not null, default(0), check(quantity_in_stock>=0)
  reorder_level: int, check(reorder_level>=0)
  constraints:
    FK: product_id: Product(product_id), CASCADE
    unique: UQ_ProductVariant, product_id, volume
  sample:
    - product_id: 1, volume: 100, price: 2428800.00, discount_price: 2428800.00, quantity_in_stock: 0, reorder_level: 0

InventoryTransaction:
  inventory_transaction_id: int, PK, identity
  product_variant_id: int, not null
  performed_by: int, not null
  transaction_type: varchar(20), not null, check(transaction_type IN ('import','export','adjust','sell','combine'))
  transaction_date: datetime, not null, check(transaction_date<=GETDATE())
  before_quantity: int, check(before_quantity>=0)
  quantity: int, not null, check(quantity>0)
  after_quantity: int, check(after_quantity>=0)
  reason: nvarchar(max)
  note: nvarchar(max)
  cost_price: decimal(10,2), check(cost_price>=0)
  constraints:
    FK:
      - product_variant_id: ProductVariant(product_variant_id)
      - performed_by: Employee(employee_id)
  sample:
    - product_variant_id: 1, performed_by: 1, transaction_type: import, transaction_date: 2024-01-10 09:00:00, before_quantity: 0, quantity: 200, after_quantity: 200, reason: Nhập hàng đầu năm, note: PO#2401-001, cost_price: 450000.00

ProductDetail:
  product_detail_id: int, PK, identity
  product_id: int, not null
  detail_name: varchar(50), not null, check(detail_name IN ('tone_scent','style','top_note','middle_note','base_note','longevity','projection','season','time_of_day','suitable_age','suitable_gender'))
  detail_value: nvarchar(255), not null, check(LEN(LTRIM(RTRIM(detail_value)))>0)
  note: nvarchar(max)
  constraints:
    FK: product_id: Product(product_id), CASCADE
    unique: UQ_ProductDetail, product_id, detail_name, detail_value
  sample:
    - product_id: 1058, detail_name: top_note, detail_value: pink pepper

Promotion:
  promotion_id: int, PK, identity
  promotion_name: nvarchar(100), not null, check(LEN(LTRIM(RTRIM(promotion_name)))>0)
  description: nvarchar(max)
  start_date: date, not null
  end_date: date, check(end_date>=start_date)
  discount_type: varchar(20), not null, default(percentage), check(discount_type IN ('percentage','fixed_amount','free_shipping'))
  discount_value: decimal(10,2), check(discount_value>=0)
  status: varchar(20), not null, default(active), check(status IN ('active','inactive','expired'))
  usage_limit: int, check(usage_limit>=0)
  sample:
    - promotion_name: VALENTINE_SALE_10, description: Giảm giá 10% mừng Valentine, start_date: 2025-02-10, end_date: 2025-02-15, discount_type: percentage, discount_value: 10.00, status: expired, usage_limit: 100

ProductPromotion:
  product_promotion_id: int, PK, identity
  product_id: int, not null
  promotion_id: int, not null
  condition_json: nvarchar(max), check(ISJSON(condition_json)=1)
  max_discount_amount: decimal(10,2), check(max_discount_amount>=0)
  start_date: date, not null
  end_date: date, check(end_date>=start_date)
  status: varchar(20), not null, default(active), check(status IN ('active','inactive','expired'))
  constraints:
    FK:
      - product_id: Product(product_id), CASCADE
      - promotion_id: Promotion(promotion_id), CASCADE
    unique: UQ_ProductPromotion, product_id, promotion_id
  sample:
    - product_id: 3, promotion_id: 1, start_date: 2025-02-10, end_date: 2025-02-15, status: expired

Customer:
  customer_id: int, PK, identity
  username: varchar(50)
  password: varchar(255)
  name: nvarchar(100), not null, check(LEN(LTRIM(RTRIM(name)))>0)
  phone_number: varchar(20), unique, check(phone_number NOT LIKE '%[^0-9 ()+-]%')
  email: varchar(100), unique, check(email LIKE '%_@__%.__%')
  street: nvarchar(255)
  ward: nvarchar(50)
  district: nvarchar(50)
  city: nvarchar(50)
  shipping_note: nvarchar(50)
  note: nvarchar(max)
  rating: int, not null, default(10), check(rating>=0)
  status: varchar(20), not null, default(active), check(status IN ('active','inactive','banned'))
  loyalty_points: int, not null, default(0), check(loyalty_points>=0)
  create_at: datetime, not null, default(GETDATE())
  update_at: datetime, check(update_at>=create_at)
  sample:
    - username: nguyenvana, password: hashed_password_a, name: Nguyễn Văn An, phone_number: 0901111222, email: <EMAIL>, street: 123 Lê Lợi, ward: Phường Bến Nghé, district: Quận 1, city: Hồ Chí Minh, shipping_note: Giao giờ hành chính, note: Khách hàng thân thiết, rating: 9, status: active, loyalty_points: 150, create_at: 2024-01-15 10:00:00

Cart:
  cart_id: int, PK, identity
  customer_id: int
  status: varchar(20), not null, default(active), check(status IN ('active','abandoned','converted'))
  total_amount: decimal(10,2), check(total_amount>=0)
  session_id: varchar(50)
  constraints:
    FK: customer_id: Customer(customer_id), CASCADE
    check: CK_Cart_CustomerSession, customer_id IS NOT NULL OR session_id IS NOT NULL
  sample:
    - customer_id: 1, status: converted, total_amount: 2753100.00

CartItem:
  cart_item_id: int, PK, identity
  cart_id: int, not null
  product_variant_id: int, not null
  quantity: int, not null, check(quantity>0)
  unit_price: decimal(10,2), not null, check(unit_price>=0)
  note: nvarchar(255)
  is_selected: bit, not null, default(1)
  constraints:
    FK:
      - cart_id: Cart(cart_id), CASCADE
      - product_variant_id: ProductVariant(product_variant_id)
    unique: UQ_CartItem, cart_id, product_variant_id
  sample:
    - cart_id: 1, product_variant_id: 5, quantity: 1, unit_price: 2753100.00, note: Tặng kèm vial thử, is_selected: 1

Wishlist:
  wishlist_id: int, PK, identity
  customer_id: int, not null
  product_variant_id: int, not null
  added_date: datetime, not null, default(GETDATE()), check(added_date<=GETDATE())
  constraints:
    FK:
      - customer_id: Customer(customer_id), CASCADE
      - product_variant_id: ProductVariant(product_variant_id), CASCADE
    unique: UQ_Wishlist_Customer_ProductVariant, customer_id, product_variant_id
  sample:
    - customer_id: 1, product_variant_id: 15, added_date: 2024-11-01 10:15:00

Order:
  order_id: int, PK, identity
  customer_id: int, not null
  employee_id: int
  order_date: datetime, not null, default(GETDATE()), check(order_date<=GETDATE())
  total_amount: decimal(10,2), check(total_amount>=0)
  shipping_fee: decimal(10,2), check(shipping_fee>=0)
  order_status: varchar(20), not null, default(pending), check(order_status IN ('pending','processing','shipping','delivered','cancelled'))
  shipping_address: nvarchar(255), not null, check(LEN(LTRIM(RTRIM(shipping_address)))>0)
  shipping_option: nvarchar(50), not null, check(LEN(LTRIM(RTRIM(shipping_option)))>0)
  note: nvarchar(max)
  estimated_delivery_date: date, check(estimated_delivery_date>=CAST(order_date AS DATE))
  constraints:
    FK:
      - customer_id: Customer(customer_id)
      - employee_id: Employee(employee_id)
  sample:
    - customer_id: 1, employee_id: 5, order_date: 2025-02-10 11:00:00, total_amount: 2753100.00, shipping_fee: 30000.00, order_status: delivered, shipping_address: 123 Lê Lợi, Quận 1, Hồ Chí Minh, shipping_option: GHTK, note: Giao giờ hành chính, estimated_delivery_date: 2025-02-12

PaymentMethod:
  payment_method_id: int, PK, identity
  method_name: varchar(50), unique, not null, check(LEN(LTRIM(RTRIM(method_name)))>0)
  description: nvarchar(max)
  sample:
    - method_name: COD, description: Thanh toán khi nhận hàng

CustomerPaymentMethod:
  customer_payment_method_id: int, PK, identity
  customer_id: int, not null
  payment_method_id: int, not null
  provider: varchar(50)
  account_number: varchar(50)
  token: varchar(255)
  is_default: bit, not null, default(0)
  constraints:
    FK:
      - customer_id: Customer(customer_id)
      - payment_method_id: PaymentMethod(payment_method_id)
    unique: UQ_CustomerPaymentMethod, customer_id, payment_method_id, account_number
    unique_index: UQ_CustomerPaymentMethod_IsDefault_True, customer_id, is_default, where(is_default=1)
  sample:
    - customer_id: 1, payment_method_id: 2, provider: Vietcombank, account_number: *************, is_default: 1

Payment:
  payment_id: int, PK, identity
  payment_method_id: int, not null
  order_id: int, not null
  payment_date: datetime, not null, check(payment_date<=GETDATE())
  amount: decimal(10,2), not null, check(amount>=0)
  payment_status: varchar(20), not null, default(pending), check(payment_status IN ('pending','completed','failed','refunded'))
  transaction_id: varchar(50)
  note: nvarchar(max)
  currency: varchar(10), not null, default(VND), check(LEN(LTRIM(RTRIM(currency)))>0)
  constraints:
    FK:
      - order_id: Order(order_id)
      - payment_method_id: PaymentMethod(payment_method_id)
  sample:
    - payment_method_id: 2, order_id: 1, payment_date: 2025-02-10 11:05:00, amount: 2783100.00, payment_status: completed, transaction_id: VCB_TRX_001, note: Customer VCB transfer, currency: VND

Shipment:
  shipment_id: int, PK, identity
  order_id: int, not null
  shipping_provider: varchar(50)
  tracking_number: varchar(50)
  shipment_status: varchar(20), not null, default(pending), check(shipment_status IN ('pending','in_transit','delivered','failed'))
  shipping_cost: decimal(10,2), check(shipping_cost>=0)
  shipping_date: date, check(shipping_date<=CAST(GETDATE() AS DATE))
  estimated_delivery_date: date, check(estimated_delivery_date>=shipping_date)
  delivery_date: date, check(delivery_date>=shipping_date)
  constraints:
    FK: order_id: Order(order_id)
  sample:
    - order_id: 1, shipping_provider: GHTK, tracking_number: GHTK11223344, shipment_status: delivered, shipping_cost: 30000.00, shipping_date: 2025-02-10, estimated_delivery_date: 2025-02-12, delivery_date: 2025-02-11

OrderItem:
  order_item_id: int, PK, identity
  order_id: int, not null
  product_variant_id: int, not null
  quantity: int, not null, check(quantity>0)
  unit_price: decimal(10,2), not null, check(unit_price>=0)
  note: nvarchar(255)
  constraints:
    FK:
      - order_id: Order(order_id)
      - product_variant_id: ProductVariant(product_variant_id)
    unique: UQ_OrderItem, order_id, product_variant_id
  sample:
    - order_id: 1, product_variant_id: 5, quantity: 1, unit_price: 2753100.00, note: Complimentary vial included

OrderPromotion:
  order_promotion_id: int, PK, identity
  order_id: int, not null
  promotion_id: int, not null
  discount_amount: decimal(10,2), check(discount_amount>=0)
  note: nvarchar(max)
  constraints:
    FK:
      - order_id: Order(order_id)
      - promotion_id: Promotion(promotion_id)
    unique: UQ_OrderPromotion, order_id, promotion_id
  sample:
    - order_id: 2, promotion_id: 3, discount_amount: 0.00, note: Applied Freeship HCM

Conversation:
  conversation_id: int, PK, identity
  start_time: datetime, not null, default(GETDATE())
  end_time: datetime, check(end_time>=start_time)
  status: varchar(20), not null, default(active), check(status IN ('active','done'))
  rating: varchar(20), check(rating IN ('bad','average','good'))
  channel: varchar(50)
  constraints:
    check:
      - CK_Conversation_StatusEndTime_Logic, status!='done' OR end_time IS NOT NULL
      - CK_Conversation_RatingStatus_Logic, rating IS NULL OR status='done'
  sample:
    - start_time: 2025-04-15 09:00:00, end_time: 2025-04-15 09:15:00, status: done, rating: good, channel: Facebook

ChatMessage:
  chat_message_id: int, PK, identity
  conversation_id: int, not null
  sender_id: int
  sender_type: varchar(20), not null, check(sender_type IN ('employee','customer','bot'))
  receiver_id: int
  receiver_type: varchar(20), not null, check(receiver_type IN ('employee','customer','bot'))
  content: nvarchar(max), not null, check(LEN(LTRIM(RTRIM(content)))>0)
  timestamp: datetime, not null, default(GETDATE()), check(timestamp<=GETDATE())
  constraints:
    FK: conversation_id: Conversation(conversation_id)
  sample:
    - conversation_id: 1, sender_id: 2, sender_type: customer, receiver_id: 6, receiver_type: employee, content: Shop ơi, cho mình hỏi về mùi hương của YSL Libre?, timestamp: 2025-04-15 09:00:15
```

### Key Components:

* **Admin UI**: Located in `admin/`, built with React.js and Vite.
* **Frontend UI (User Interface)**: Located in `frontend/`, built with React.js and Vite.
* **Backend**: Located in `backend/`, built using Spring Boot with API controllers, services, and repositories.

### Database Migrations:

* `V1__init_schema.sql`: Defines the initial database schema.
* `V2__add_constraint.sql`: Adds constraints to the database schema.

### Backend Configuration:

* **API and DB Configurations**: Located in `application.properties`.
* **Spring Boot Configuration Files**: `ApiConfig.java`, `SecurityConfig.java`, `WebConfig.java`.


## Code Style Guidelines

### Frontend (React.js):

* **Components**: Use **functional components** with hooks.
* **Component Names**: Use **PascalCase** (e.g., `ProductList`).
* **File Structure**: Place components in `admin/components/` or `frontend/components/`.
* **TypeScript**: Always add type definitions for props and state.
* **Styling**: Use **Tailwind CSS** for mobile-first design.

### Backend (Spring Boot):

* **API Conventions**: Follow RESTful conventions for APIs (e.g., `/api/v1/products`).
* **Variable Naming**: Use **camelCase** for variables.
* **Annotations**: Use `@ApiOperation` for API endpoint documentation.
* **Error Handling**: Use `try-catch` blocks and return proper HTTP status codes (e.g., 400, 500).
* **Authentication**: Follow authentication setup in `SecurityConfig.java`.

### Database (SQL Server):

* **SQL Queries**: Write queries following the schema defined in `db/migration/`.
* **Parameterization**: Use **parameterized queries** to prevent SQL injection.

## Development Workflow

### Before Coding:

1. **Read Database Schema**: Understand the database schema by reviewing `V1__init_schema.sql` and `V2__add_constraint.sql`.
2. **Read Configurations**: Check `application.properties` for API base URL, logging, and database configurations.
3. **Understand Business Logic**: Review Java code in `controller/`, `service/`, and `repository/` to understand API endpoints, business logic, and authentication setup.
4. **Ask Clarification**: If any information is missing, ask for clarification (e.g., endpoint details or authentication requirements).

### Writing Code:

1. **Follow Project Structure**: Ensure your code follows the structure outlined in the project.
2. **Write Scalable Code**: Focus on writing scalable, maintainable, and secure code.
3. **Avoid Hard-Coding**: Never hard-code sensitive information like API keys or database credentials.

### Calling APIs:

1. **Check Application Properties**: Read `application.properties` for base URLs.
2. **Check API Endpoints**: Look into `controller/` for paths, HTTP methods, payload formats, and headers.
3. **Authentication**: Follow authentication setup in `SecurityConfig.java` (e.g., use `Authorization: Bearer <token>` if JWT is required).
4. **Curl Commands**: Construct and execute `curl` commands with proper headers, JSON payloads, and query parameters.

### Testing Code:

* **Backend**: Test APIs using `curl` via the integrated terminal in Windsurf (e.g., `curl -v -H "Authorization: Bearer <token>" "http://localhost:8080/api/v1/..."`).
* **Frontend**: Test components at `http://localhost:5173`.
* **Debugging**: In case of failures, analyze the errors and adjust the code or `curl` commands as needed.

## Reporting and Debugging

If you encounter an issue that you cannot resolve after multiple attempts:

1. **Report**: Provide a detailed report including:

   * HTTP status code and logs.
   * The `curl` command used and its response.
   * Root cause analysis.
   * Debug steps and fixes attempted.

2. **Do Not Stop**: Continue troubleshooting and retesting until the code works as expected (API returns 200 OK, frontend renders correctly).

## Optimization Rules

1. **Minimize Credit Usage**: Ensure the entire task (coding, testing, and fixing) is handled in a single prompt.
2. **Quick Tests**: Prioritize quick tests, like `curl`, before running the full app.
3. **Use Integrated Tools**: Leverage tools like Windsurf’s terminal, Test Runner, and Debug Console for automation.
4. **Avoid Intermediate Steps**: Do not propose plans or ask for feedback unless absolutely necessary.

## Constraints

* Do not modify `application.properties` or `SecurityConfig.java` without prior approval.
* Do not stop until the code is confirmed to be functional (e.g., API returns `200 OK`, UI renders correctly).
* Always follow authentication and API setup as defined in `SecurityConfig.java`.
