	(2) Tăng/giảm số lượng sản phẩm																														
		1. <PERSON>hi người dùng nhấn vào nút "+" hoặc "-" sẽ tăng hoặc giảm số lượng sản phẩm đi 1 đơn vị																													
		2. <PERSON><PERSON><PERSON> số lượng đang là 1 nút "-" sẽ bị vô hiệu hóa																													
		3. <PERSON><PERSON><PERSON> tăng số lượng đến mức tối đa(10 sản phẩm mỗi loại) trong kho hàng sẽ vô hiệu hóa nút "+"																													
		4. Nhấn button "Tiếp tục mua sắm" sẽ đưa người dùng về danh sách sản phẩm để thêm sản phẩm mới 																													
		Cập nhật UI ngay lập tức mỗi lần nhấn																													


- Ở phần thanh toán thành công
 1.Chỉ clear localStorage nếu chắc chắn đơn hàng đã được lưu thành công
Hiện tại, bạn setTimeout 2s rồi xóa luôn — lỡ trong 2s đó API chưa lưu xong thì mất data.

2. <PERSON><PERSON> lý fallback nếu user reload trang trước khi gửi được đơn
Ví dụ:

User thanh toán xong → redirect về trang xác nhận → trang reload trước khi lưu đơn → mất hết localStorage.

👉 Gợi ý: Có thể thêm 1 flag như orderSaved = true vào localStorage sau khi lưu thành công. Lần sau load lại check flag này để không gửi lại đơn nữa.